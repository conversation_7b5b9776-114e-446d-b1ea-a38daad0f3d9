import boto3
import json
import arg<PERSON><PERSON>

def invoke_model(prompt, max_tokens=512, temperature=0.7):
    # Cấu hình
    REGION = "us-west-2"
    
    # Lấy ARN của mô hình
    bedrock = boto3.client(service_name="bedrock", region_name=REGION)
    response = bedrock.list_imported_models()
    model_arn = None
    
    for model in response.get("importedModelSummaries", []):
        if model.get("importedModelName") == "qwen-32b-original":
            model_arn = model.get("importedModelArn")
            break
    
    if not model_arn:
        print("Không tìm thấy mô hình qwen-32b-original. Vui lòng kiểm tra lại.")
        return
    
    # Tạo client
    bedrock_runtime = boto3.client(service_name="bedrock-runtime", region_name=REGION)
    
    # Tạo request
    request = {
        "prompt": prompt,
        "max_tokens": max_tokens,
        "temperature": temperature
    }
    
    # <PERSON><PERSON><PERSON> mô hình
    try:
        response = bedrock_runtime.invoke_model(
            modelId=model_arn,
            body=json.dumps(request),
            contentType="application/json"
        )
        
        # Xử lý response
        response_body = json.loads(response["body"].read())
        
        # In kết quả
        if "choices" in response_body and len(response_body["choices"]) > 0:
            text = response_body["choices"][0]["text"]
            print("\nKết quả:")
            print(text)
            
            # Lưu kết quả vào file
            with open("output_32b.txt", "w") as f:
                f.write(text)
            print("\nĐã lưu kết quả vào file output_32b.txt")
        else:
            print("Không có kết quả từ mô hình.")
            print("Response:", json.dumps(response_body, indent=2))
        
    except Exception as e:
        print(f"Lỗi khi gọi mô hình: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Gọi mô hình Qwen2.5-VL-32B-Instruct")
    parser.add_argument("--prompt", type=str, default="Describe a futuristic city in 100 words.", 
                        help="Prompt để gửi đến mô hình")
    parser.add_argument("--max_tokens", type=int, default=512, 
                        help="Số lượng token tối đa để tạo ra")
    parser.add_argument("--temperature", type=float, default=0.7, 
                        help="Nhiệt độ để kiểm soát tính ngẫu nhiên của kết quả")
    
    args = parser.parse_args()
    invoke_model(args.prompt, args.max_tokens, args.temperature)
