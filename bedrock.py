import boto3
import json
import os
import tarfile
import time
from huggingface_hub import login, snapshot_download
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

# Cấu hình
HF_TOKEN = "*************************************"  # Thay bằng Hugging Face token
AWS_REGION = "us-west-2"
S3_BUCKET = "dbiz-llm"  # Thay bằng tên bucket độc nhất
MODEL_NAME = "Qwen/Qwen2.5-VL-32B-Instruct-AWQ"  # Mô hình AWQ từ Hugging Face
CUSTOM_MODEL_NAME = "qwen-32b-vl-awq"
ROLE_ARN = "arn:aws:iam::737232469904:role/aws-service-role/support.amazonaws.com/AWSServiceRoleForSupport"  # Sử dụng service role có sẵn
LOCAL_MODEL_DIR = "./qwen-awq-model"
S3_MODEL_PATH = f"s3://{S3_BUCKET}/qwen-awq-model.tar.gz"
ENDPOINT_NAME = "qwen-32b-awq-endpoint"
JOB_NAME = "import-qwen-awq"

# Bước 1: Kiểm tra mô hình local hoặc tải từ Hugging Face
print("Kiểm tra mô hình local hoặc tải từ Hugging Face...")
try:
    if os.path.exists(LOCAL_MODEL_DIR) and os.listdir(LOCAL_MODEL_DIR):
        print(f"Mô hình đã có tại {LOCAL_MODEL_DIR}. Bỏ qua tải.")
    else:
        login(token=HF_TOKEN)
        snapshot_download(
            repo_id=MODEL_NAME,
            local_dir=LOCAL_MODEL_DIR,
            local_dir_use_symlinks=False,
            token=HF_TOKEN
        )
        print(f"Đã tải mô hình {MODEL_NAME} vào {LOCAL_MODEL_DIR}.")
except Exception as e:
    print(f"Lỗi khi tải mô hình: {e}")
    exit(1)

# Bước 3: Đóng gói mô hình
print("Đóng gói mô hình...")
try:
    with tarfile.open("qwen-awq-model.tar.gz", "w:gz") as tar:
        tar.add(LOCAL_MODEL_DIR, arcname=os.path.basename(LOCAL_MODEL_DIR))
    print("Đã tạo file qwen-awq-model.tar.gz.")
except Exception as e:
    print(f"Lỗi khi đóng gói: {e}")
    exit(1)

# Bước 4: Tải lên S3
print("Tải mô hình lên S3...")
s3_client = boto3.client("s3", region_name=AWS_REGION)
try:
    try:
        s3_client.create_bucket(
            Bucket=S3_BUCKET,
            CreateBucketConfiguration={"LocationConstraint": AWS_REGION}
        )
    except (s3_client.exceptions.BucketAlreadyOwnedByYou, s3_client.exceptions.BucketAlreadyExists):
        pass
    s3_client.upload_file("qwen-awq-model.tar.gz", S3_BUCKET, "qwen-awq-model.tar.gz")
    print(f"Đã tải mô hình lên {S3_MODEL_PATH}.")
except Exception as e:
    print(f"Lỗi khi tải lên S3: {e}")
    exit(1)

# Bước 5: Nhập mô hình vào Bedrock
print("Nhập mô hình vào Bedrock...")
try:
    print("Để nhập mô hình vào Bedrock, bạn cần một IAM role có quyền truy cập vào S3 và có mối quan hệ tin cậy với Bedrock.")
    print("Vui lòng liên hệ với quản trị viên AWS của bạn để tạo một IAM role phù hợp.")
    print("\nSau khi có IAM role, bạn có thể chạy lệnh sau để nhập mô hình:")
    print(f"\naws bedrock create-model-import-job --job-name {JOB_NAME} --model-name {CUSTOM_MODEL_NAME} --model-source-s3-uri {S3_MODEL_PATH} --model-data-format HUGGING_FACE --role-arn <IAM_ROLE_ARN> --region {AWS_REGION}")
    print("\nĐể kiểm tra trạng thái của công việc nhập mô hình:")
    print(f"\naws bedrock describe-model-import-job --job-name {JOB_NAME} --region {AWS_REGION}")

    # Hỏi người dùng có muốn tiếp tục không
    response = input("\nBạn đã có IAM role phù hợp chưa? (y/n): ")
    if response.lower() == "y":
        role_arn = input("Nhập IAM role ARN: ")
        import subprocess
        cmd = f"aws bedrock create-model-import-job --job-name {JOB_NAME} --model-name {CUSTOM_MODEL_NAME} --model-source-s3-uri {S3_MODEL_PATH} --model-data-format HUGGING_FACE --role-arn {role_arn} --region {AWS_REGION}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"Đã tạo import job thành công.")
            print("Kiểm tra trạng thái của công việc nhập mô hình...")
            while True:
                status_cmd = f"aws bedrock describe-model-import-job --job-name {JOB_NAME} --region {AWS_REGION}"
                status_result = subprocess.run(status_cmd, shell=True, capture_output=True, text=True)
                if status_result.returncode == 0:
                    status_data = json.loads(status_result.stdout)
                    job_status = status_data.get("jobStatus")
                    print(f"Import job status: {job_status}")
                    if job_status in ["COMPLETED", "FAILED", "STOPPED"]:
                        if job_status != "COMPLETED":
                            print("Import job thất bại.")
                            exit(1)
                        break
                time.sleep(30)
        else:
            print(f"Lỗi khi tạo import job: {result.stderr}")
            exit(1)
    else:
        print("Bạn cần có IAM role phù hợp để tiếp tục.")
        exit(0)
except Exception as e:
    print(f"Lỗi khi nhập mô hình: {e}")
    exit(1)

# Bước 6: Tạo endpoint
print("Tạo endpoint...")
try:
    bedrock_client.create_model_invocation_endpoint(
        ModelId=CUSTOM_MODEL_NAME,
        EndpointName=ENDPOINT_NAME,
        InferenceType="ON_DEMAND"
    )
    print(f"Đã tạo endpoint: {ENDPOINT_NAME}")
except Exception as e:
    print(f"Lỗi khi tạo endpoint: {e}")
    exit(1)

# Bước 7: Gửi request inference
print("Gửi request inference...")
runtime_client = boto3.client("bedrock-runtime", region_name=AWS_REGION)
try:
    response = runtime_client.invoke_model(
        modelId=CUSTOM_MODEL_NAME,
        body=json.dumps({
            "prompt": "Describe a futuristic city in 100 words.",
            "max_tokens": 512,
            "temperature": 0.7
        }),
        contentType="application/json"
    )
    result = json.loads(response["body"].read().decode())
    print("Kết quả inference:", result)
except Exception as e:
    print(f"Lỗi khi gửi request: {e}")
    exit(1)

print("Triển khai hoàn tất!")