import boto3
import json

# <PERSON><PERSON><PERSON> hình
MODEL_ID = "arn:aws:bedrock:us-west-2:737232469904:imported-model/ev2u66sfdhsb"
REGION = "us-west-2"

# Tạo client
bedrock_runtime = boto3.client(service_name="bedrock-runtime", region_name=REGION)

# Th<PERSON> các định dạng request khác nhau
requests = [
    # Định dạng 1: prompt
    {
        "prompt": "Describe a futuristic city in 100 words.",
        "max_tokens": 512,
        "temperature": 0.7
    },
    # Định dạng 2: messages
    {
        "messages": [
            {
                "role": "user",
                "content": "Describe a futuristic city in 100 words."
            }
        ],
        "max_tokens": 512,
        "temperature": 0.7
    },
    # Định dạng 3: inputText (Amazon Titan)
    {
        "inputText": "Describe a futuristic city in 100 words.",
        "textGenerationConfig": {
            "maxTokenCount": 512,
            "temperature": 0.7
        }
    },
    # Đ<PERSON>nh dạng 4: Anthropic Claude
    {
        "anthropic_version": "bedrock-2023-05-31",
        "max_tokens": 512,
        "temperature": 0.7,
        "messages": [
            {
                "role": "user",
                "content": "Describe a futuristic city in 100 words."
            }
        ]
    }
]

# Thử từng định dạng
for i, request in enumerate(requests):
    print(f"\nThử định dạng {i+1}:")
    try:
        response = bedrock_runtime.invoke_model(
            modelId=MODEL_ID,
            body=json.dumps(request),
            contentType="application/json"
        )
        
        # Xử lý response
        response_body = json.loads(response["body"].read())
        print("Response:")
        print(json.dumps(response_body, indent=2))
        
        # Lưu response vào file
        with open(f"output_{i+1}.json", "w") as f:
            json.dump(response_body, f, indent=2)
        
        print(f"Định dạng {i+1} thành công!")
        break
    except Exception as e:
        print(f"Lỗi với định dạng {i+1}: {e}")

print("\nHoàn tất!")
