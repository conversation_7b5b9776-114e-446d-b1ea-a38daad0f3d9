from huggingface_hub import login, snapshot_download
import os

# <PERSON><PERSON><PERSON> hình
HF_TOKEN = "*************************************"  # Thay bằng Hugging Face token
MODEL_NAME = "Qwen/Qwen2.5-VL-3B-Instruct"  # Mô hình gốc từ Hugging Face
LOCAL_MODEL_DIR = "./qwen-3b-original"

# Đăng nhập vào Hugging Face
login(token=HF_TOKEN)

# Tải mô hình
print(f"Đang tải mô hình {MODEL_NAME}...")
snapshot_download(
    repo_id=MODEL_NAME,
    local_dir=LOCAL_MODEL_DIR,
    local_dir_use_symlinks=False,
    token=HF_TOKEN
)
print(f"Đã tải mô hình {MODEL_NAME} vào {LOCAL_MODEL_DIR}.")

# Liệt kê các file trong thư mục mô hình
print("\nCác file trong thư mục mô hình:")
for root, dirs, files in os.walk(LOCAL_MODEL_DIR):
    level = root.replace(LOCAL_MODEL_DIR, '').count(os.sep)
    indent = ' ' * 4 * level
    print(f"{indent}{os.path.basename(root)}/")
    sub_indent = ' ' * 4 * (level + 1)
    for file in files:
        print(f"{sub_indent}{file}")
