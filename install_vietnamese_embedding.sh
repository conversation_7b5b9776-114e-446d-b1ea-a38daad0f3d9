#!/bin/bash

# Script to install Vietnamese_Embedding_v2 model from AITeamVN
# This script performs the installation directly without using Ansible

# Configuration
MODEL_REPO="AITeamVN/Vietnamese_Embedding_v2"
MODEL_NAME=$(echo $MODEL_REPO | awk -F'/' '{print tolower($2)}' | tr '_' '-')
MODEL_DIR="/datastores/hf/${MODEL_REPO/\//_}"
MODEL_FORMAT="f16"
CONTEXT_SIZE=2048

echo "Installing Vietnamese Embedding model from $MODEL_REPO"
echo "Model will be installed as: $MODEL_NAME"
echo "Model directory: $MODEL_DIR"

# Create model directory
mkdir -p "$MODEL_DIR"

# Install dependencies
echo "Installing dependencies..."
apt-get update
apt-get install -y git git-lfs cmake build-essential python3-dev python3-pip python3-venv libcurl4-openssl-dev

# Create Python virtual environment
echo "Creating Python virtual environment..."
python3 -m venv "${MODEL_DIR}_venv"

# Install Python dependencies
echo "Installing Python packages..."
source "${MODEL_DIR}_venv/bin/activate"
pip install torch transformers sentencepiece protobuf requests numpy

# Initialize Git LFS
echo "Initializing Git LFS..."
git lfs install

# Clone the model repository
echo "Downloading model from Hugging Face..."
if [ ! -f "$MODEL_DIR/model.safetensors" ]; then
  git clone "https://huggingface.co/$MODEL_REPO" "$MODEL_DIR"
fi

# Clone llama.cpp repository
echo "Cloning llama.cpp repository..."
if [ ! -d "${MODEL_DIR}/../llama.cpp" ]; then
  git clone https://github.com/ggerganov/llama.cpp.git "${MODEL_DIR}/../llama.cpp"
fi

# Build llama.cpp
echo "Building llama.cpp..."
if [ ! -f "${MODEL_DIR}/../llama.cpp/build/bin/llama-cli" ]; then
  cd "${MODEL_DIR}/../llama.cpp"
  mkdir -p build
  cd build
  cmake -DLLAMA_CURL=OFF ..
  cmake --build . --config Release
fi

# Convert model to GGUF format
echo "Converting model to GGUF format..."
if [ ! -f "$MODEL_DIR/$MODEL_NAME.gguf" ]; then
  source "${MODEL_DIR}_venv/bin/activate"
  cd "${MODEL_DIR}/../llama.cpp"
  python convert_hf_to_gguf.py --outfile "$MODEL_DIR/$MODEL_NAME.gguf" --outtype "$MODEL_FORMAT" "$MODEL_DIR"
fi

# Create Modelfile for Ollama
echo "Creating Modelfile for Ollama..."
cat > "$MODEL_DIR/Modelfile" << EOF
FROM $MODEL_DIR/$MODEL_NAME.gguf
TEMPLATE "{{ .Prompt }}"
PARAMETER stop ""
PARAMETER num_ctx $CONTEXT_SIZE
EOF

# Create model in Ollama
echo "Creating model in Ollama..."
if ! ollama list | grep -q "$MODEL_NAME"; then
  cd "$MODEL_DIR" && ollama create "$MODEL_NAME" -f Modelfile
fi

# Test the model
echo "Testing the model..."
source "${MODEL_DIR}_venv/bin/activate"
python3 -c "
import requests
import json

response = requests.post('http://localhost:11434/api/embeddings',
                       json={'model': '$MODEL_NAME',
                            'prompt': 'Trí tuệ nhân tạo là gì'})

# Just print the first 5 dimensions of the embedding to verify it works
embedding = response.json()['embedding'][:5]
print(f'First 5 dimensions of embedding: {embedding}')
"

echo "Installation complete!"
