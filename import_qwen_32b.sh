#!/bin/bash

# Script tự động tải và cài đặt mô hình Qwen2.5-VL-32B-Instruct lên AWS Bedrock
# Lưu ý: Script này yêu cầu Python, AWS CLI, và Hugging Face Hub đã được cài đặt

set -e  # Dừng script nếu có lỗi

# Cấu hình
HF_TOKEN="*************************************"
MODEL_NAME="Qwen/Qwen2.5-VL-32B-Instruct"
LOCAL_MODEL_DIR="./qwen-32b-original"
S3_BUCKET="dbiz-llm"
S3_PREFIX="qwen-32b-original"
S3_URI="s3://${S3_BUCKET}/${S3_PREFIX}/"
AWS_REGION="us-west-2"
BEDROCK_MODEL_NAME="qwen-32b-original"
IAM_ROLE_ARN="arn:aws:iam::737232469904:role/BedrockImportRole"
JOB_NAME="import-qwen-32b-original"

echo "=== Bắt đầu quá trình nhập mô hình Qwen2.5-VL-32B-Instruct vào AWS Bedrock ==="

# Bước 1: Tải mô hình từ Hugging Face
echo "Bước 1: Tải mô hình từ Hugging Face..."
python3 -c "
from huggingface_hub import login, snapshot_download
import os

# Đăng nhập vào Hugging Face
login(token='${HF_TOKEN}')

# Tải mô hình
print(f'Đang tải mô hình ${MODEL_NAME}...')
snapshot_download(
    repo_id='${MODEL_NAME}',
    local_dir='${LOCAL_MODEL_DIR}',
    local_dir_use_symlinks=False,
    token='${HF_TOKEN}'
)
print(f'Đã tải mô hình ${MODEL_NAME} vào ${LOCAL_MODEL_DIR}.')

# Liệt kê các file trong thư mục mô hình
print('\nCác file trong thư mục mô hình:')
for root, dirs, files in os.walk('${LOCAL_MODEL_DIR}'):
    level = root.replace('${LOCAL_MODEL_DIR}', '').count(os.sep)
    indent = ' ' * 4 * level
    print(f'{indent}{os.path.basename(root)}/')
    sub_indent = ' ' * 4 * (level + 1)
    for file in files:
        print(f'{sub_indent}{file}')
"

# Bước 2: Tạo thư mục sạch chỉ chứa các file cần thiết
echo "Bước 2: Tạo thư mục sạch..."
mkdir -p qwen-32b-clean
cd ${LOCAL_MODEL_DIR} && cp -r $(ls -A | grep -v "^\." | xargs) ../qwen-32b-clean/
cd ..

# Bước 3: Tải mô hình lên S3
echo "Bước 3: Tải mô hình lên S3..."
aws s3 cp qwen-32b-clean/ ${S3_URI} --recursive

# Bước 4: Tạo công việc nhập mô hình vào Bedrock
echo "Bước 4: Tạo công việc nhập mô hình vào Bedrock..."
aws bedrock create-model-import-job \
  --job-name ${JOB_NAME} \
  --imported-model-name ${BEDROCK_MODEL_NAME} \
  --model-data-source "{\"s3DataSource\": {\"s3Uri\": \"${S3_URI}\"}}" \
  --role-arn ${IAM_ROLE_ARN} \
  --region ${AWS_REGION}

# Bước 5: Kiểm tra trạng thái của công việc nhập mô hình
echo "Bước 5: Kiểm tra trạng thái của công việc nhập mô hình..."
python3 -c "
import subprocess
import json
import time

JOB_NAME = '${JOB_NAME}'
REGION = '${AWS_REGION}'
BEDROCK_MODEL_NAME = '${BEDROCK_MODEL_NAME}'

def check_status():
    cmd = f'aws bedrock get-model-import-job --job-identifier {JOB_NAME} --region {REGION}'
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        data = json.loads(result.stdout)
        status = data.get('status')
        last_modified = data.get('lastModifiedTime')
        print(f'Trạng thái: {status}')
        print(f'Cập nhật lần cuối: {last_modified}')
        
        if status == 'COMPLETED':
            print('Nhập mô hình đã hoàn tất thành công!')
            print('Bạn có thể gọi mô hình với lệnh:')
            print(f'python3 invoke_qwen_32b.py')
            return True
        elif status in ['FAILED', 'STOPPED']:
            print('Nhập mô hình thất bại hoặc đã bị dừng.')
            if 'failureMessage' in data:
                print(f'Lỗi: {data[\"failureMessage\"]}')
            return True
    else:
        print(f'Lỗi khi kiểm tra trạng thái: {result.stderr}')
    
    return False

print('Đang kiểm tra trạng thái nhập mô hình...')
print('Quá trình này có thể mất nhiều giờ. Bạn có thể nhấn Ctrl+C để dừng kiểm tra.')
print('Để kiểm tra lại sau, sử dụng lệnh:')
print(f'aws bedrock get-model-import-job --job-identifier {JOB_NAME} --region {REGION}')

try:
    count = 0
    while count < 5:  # Chỉ kiểm tra 5 lần đầu tiên
        if check_status():
            break
        print('\nĐợi 60 giây trước khi kiểm tra lại...')
        time.sleep(60)
        count += 1
    
    if count == 5:
        print('\nĐã kiểm tra 5 lần. Quá trình nhập mô hình vẫn đang tiếp tục.')
        print('Quá trình này có thể mất nhiều giờ.')
        print('Để kiểm tra lại sau, sử dụng lệnh:')
        print(f'aws bedrock get-model-import-job --job-identifier {JOB_NAME} --region {REGION}')
except KeyboardInterrupt:
    print('\nĐã dừng kiểm tra.')
"

# Bước 6: Tạo script để gọi mô hình
echo "Bước 6: Tạo script để gọi mô hình..."
cat > invoke_qwen_32b.py << 'EOL'
import boto3
import json
import argparse

def invoke_model(prompt, max_tokens=512, temperature=0.7):
    # Cấu hình
    REGION = "us-west-2"
    
    # Lấy ARN của mô hình
    bedrock = boto3.client(service_name="bedrock", region_name=REGION)
    response = bedrock.list_imported_models()
    model_arn = None
    
    for model in response.get("importedModelSummaries", []):
        if model.get("importedModelName") == "qwen-32b-original":
            model_arn = model.get("importedModelArn")
            break
    
    if not model_arn:
        print("Không tìm thấy mô hình qwen-32b-original. Vui lòng kiểm tra lại.")
        return
    
    # Tạo client
    bedrock_runtime = boto3.client(service_name="bedrock-runtime", region_name=REGION)
    
    # Tạo request
    request = {
        "prompt": prompt,
        "max_tokens": max_tokens,
        "temperature": temperature
    }
    
    # Gọi mô hình
    try:
        response = bedrock_runtime.invoke_model(
            modelId=model_arn,
            body=json.dumps(request),
            contentType="application/json"
        )
        
        # Xử lý response
        response_body = json.loads(response["body"].read())
        
        # In kết quả
        if "choices" in response_body and len(response_body["choices"]) > 0:
            text = response_body["choices"][0]["text"]
            print("\nKết quả:")
            print(text)
            
            # Lưu kết quả vào file
            with open("output_32b.txt", "w") as f:
                f.write(text)
            print("\nĐã lưu kết quả vào file output_32b.txt")
        else:
            print("Không có kết quả từ mô hình.")
            print("Response:", json.dumps(response_body, indent=2))
        
    except Exception as e:
        print(f"Lỗi khi gọi mô hình: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Gọi mô hình Qwen2.5-VL-32B-Instruct")
    parser.add_argument("--prompt", type=str, default="Describe a futuristic city in 100 words.", 
                        help="Prompt để gửi đến mô hình")
    parser.add_argument("--max_tokens", type=int, default=512, 
                        help="Số lượng token tối đa để tạo ra")
    parser.add_argument("--temperature", type=float, default=0.7, 
                        help="Nhiệt độ để kiểm soát tính ngẫu nhiên của kết quả")
    
    args = parser.parse_args()
    invoke_model(args.prompt, args.max_tokens, args.temperature)
EOL

echo "=== Hoàn tất! ==="
echo "Mô hình đang được nhập vào AWS Bedrock. Quá trình này có thể mất nhiều giờ."
echo "Để kiểm tra trạng thái, sử dụng lệnh:"
echo "aws bedrock get-model-import-job --job-identifier ${JOB_NAME} --region ${AWS_REGION}"
echo ""
echo "Sau khi mô hình được nhập thành công, bạn có thể gọi mô hình với lệnh:"
echo "python3 invoke_qwen_32b.py --prompt \"Prompt của bạn\""
