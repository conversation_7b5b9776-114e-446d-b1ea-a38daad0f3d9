import boto3
import json

# <PERSON><PERSON><PERSON> hình
MODEL_ID = "arn:aws:bedrock:us-west-2:737232469904:imported-model/ev2u66sfdhsb"
REGION = "us-west-2"

# Tạo client
bedrock_runtime = boto3.client(service_name="bedrock-runtime", region_name=REGION)

# Tạo request
request = {
    "messages": [
        {
            "role": "user",
            "content": "Describe a futuristic city in 100 words."
        }
    ],
    "max_tokens": 512,
    "temperature": 0.7
}

# G<PERSON>i mô hình
try:
    response = bedrock_runtime.invoke_model(
        modelId=MODEL_ID,
        body=json.dumps(request),
        contentType="application/json"
    )
    
    # Xử lý response
    response_body = json.loads(response["body"].read())
    print("Response:")
    print(json.dumps(response_body, indent=2))
    
    # Lưu response vào file
    with open("output.json", "w") as f:
        json.dump(response_body, f, indent=2)
    
except Exception as e:
    print(f"Error: {e}")
