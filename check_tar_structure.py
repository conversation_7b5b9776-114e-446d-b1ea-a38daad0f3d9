import boto3
import tarfile
import io
import os

# C<PERSON>u hình
AWS_REGION = "us-west-2"
S3_BUCKET = "dbiz-llm"
TAR_FILE = "qwen-awq-model.tar.gz"

# Tạo S3 client
s3_client = boto3.client("s3", region_name=AWS_REGION)

# Tạo thư mục tạm thời để lưu trữ các file cần thiết
os.makedirs("temp", exist_ok=True)

# Tải file config.json từ trong tar.gz
print(f"Kiểm tra cấu trúc của file {TAR_FILE}...")

try:
    # Tải một phần nhỏ của file tar.gz để đọc cấu trúc
    response = s3_client.get_object(
        Bucket=S3_BUCKET,
        Key=TAR_FILE,
        Range="bytes=0-10485760"  # Tải 10MB đầu tiên
    )
    
    # Đọc dữ liệu
    data = response["Body"].read()
    
    # Tạo file tar.gz tạm thời
    with open("temp/partial.tar.gz", "wb") as f:
        f.write(data)
    
    # Mở file tar.gz và liệt kê các file bên trong
    try:
        with tarfile.open("temp/partial.tar.gz", "r:gz") as tar:
            print("Các file trong tar.gz:")
            for member in tar.getmembers():
                print(f"  {member.name}")
            
            # Kiểm tra xem có file config.json không
            config_files = [m for m in tar.getmembers() if m.name.endswith("config.json")]
            if config_files:
                print("\nTìm thấy file config.json:")
                for config_file in config_files:
                    print(f"  {config_file.name}")
                    
                    # Trích xuất file config.json
                    tar.extract(config_file, path="temp")
                    print(f"Đã trích xuất {config_file.name} vào thư mục temp")
            else:
                print("\nKhông tìm thấy file config.json trong phần đầu của tar.gz")
                print("Có thể file config.json nằm ở vị trí khác trong file tar.gz")
    except tarfile.ReadError as e:
        print(f"Lỗi khi đọc file tar.gz: {e}")
        print("File tar.gz có thể bị hỏng hoặc không đúng định dạng")
    
except Exception as e:
    print(f"Lỗi: {e}")

print("\nGợi ý để khắc phục lỗi:")
print("1. Kiểm tra cấu trúc của file tar.gz và đảm bảo nó chứa file config.json")
print("2. Đảm bảo file tar.gz được tạo đúng cách với cấu trúc Hugging Face")
print("3. Tạo lại file tar.gz với cấu trúc đúng")
print("4. Tải lên S3 và thử lại")
