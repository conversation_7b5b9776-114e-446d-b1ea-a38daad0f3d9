import boto3
import json
import time
import subprocess

# C<PERSON>u hình
AWS_REGION = "us-west-2"
S3_BUCKET = "dbiz-llm"  # Bucket đã có sẵn
CUSTOM_MODEL_NAME = "qwen-32b-vl-awq"
S3_MODEL_PATH = f"s3://{S3_BUCKET}/qwen-awq-model.tar.gz"
ENDPOINT_NAME = "qwen-32b-awq-endpoint"
JOB_NAME = "import-qwen-awq"

print("Kiểm tra mô hình trên S3...")
try:
    # Kiểm tra xem file đã tồn tại trên S3 chưa
    cmd = f"aws s3 ls {S3_BUCKET}/qwen-awq-model.tar.gz"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        print(f"Mô hình đã tồn tại tại {S3_MODEL_PATH}")
    else:
        print(f"Không tìm thấy mô hình tại {S3_MODEL_PATH}")
        print("Vui lòng kiểm tra lại đường dẫn S3 hoặc tải mô hình lên S3 trước.")
        exit(1)
except Exception as e:
    print(f"Lỗi khi kiểm tra mô hình trên S3: {e}")
    exit(1)

# Bước 5: Nhập mô hình vào Bedrock
print("Nhập mô hình vào Bedrock...")
try:
    print("Để nhập mô hình vào Bedrock, bạn cần một IAM role có quyền truy cập vào S3 và có mối quan hệ tin cậy với Bedrock.")
    print("Vui lòng liên hệ với quản trị viên AWS của bạn để tạo một IAM role phù hợp.")
    print("\nSau khi có IAM role, bạn có thể chạy lệnh sau để nhập mô hình:")
    print(f"\naws bedrock create-model-import-job --job-name {JOB_NAME} --model-name {CUSTOM_MODEL_NAME} --model-source-s3-uri {S3_MODEL_PATH} --model-data-format HUGGING_FACE --role-arn <IAM_ROLE_ARN> --region {AWS_REGION}")
    print("\nĐể kiểm tra trạng thái của công việc nhập mô hình:")
    print(f"\naws bedrock describe-model-import-job --job-name {JOB_NAME} --region {AWS_REGION}")
    
    # Hỏi người dùng có muốn tiếp tục không
    response = input("\nBạn đã có IAM role phù hợp chưa? (y/n): ")
    if response.lower() == "y":
        role_arn = input("Nhập IAM role ARN: ")
        cmd = f"aws bedrock create-model-import-job --job-name {JOB_NAME} --model-name {CUSTOM_MODEL_NAME} --model-source-s3-uri {S3_MODEL_PATH} --model-data-format HUGGING_FACE --role-arn {role_arn} --region {AWS_REGION}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"Đã tạo import job thành công.")
            print("Kiểm tra trạng thái của công việc nhập mô hình...")
            while True:
                status_cmd = f"aws bedrock describe-model-import-job --job-name {JOB_NAME} --region {AWS_REGION}"
                status_result = subprocess.run(status_cmd, shell=True, capture_output=True, text=True)
                if status_result.returncode == 0:
                    status_data = json.loads(status_result.stdout)
                    job_status = status_data.get("jobStatus")
                    print(f"Import job status: {job_status}")
                    if job_status in ["COMPLETED", "FAILED", "STOPPED"]:
                        if job_status != "COMPLETED":
                            print("Import job thất bại.")
                            exit(1)
                        break
                time.sleep(30)
        else:
            print(f"Lỗi khi tạo import job: {result.stderr}")
            exit(1)
    else:
        print("Bạn cần có IAM role phù hợp để tiếp tục.")
        exit(0)
except Exception as e:
    print(f"Lỗi khi nhập mô hình: {e}")
    exit(1)

# Bước 6: Tạo endpoint
print("Tạo endpoint...")
try:
    cmd = f"aws bedrock create-model-invocation-endpoint --model-id {CUSTOM_MODEL_NAME} --endpoint-name {ENDPOINT_NAME} --inference-type ON_DEMAND --region {AWS_REGION}"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        print(f"Đã tạo endpoint: {ENDPOINT_NAME}")
    else:
        print(f"Lỗi khi tạo endpoint: {result.stderr}")
        exit(1)
except Exception as e:
    print(f"Lỗi khi tạo endpoint: {e}")
    exit(1)

# Bước 7: Gửi request inference
print("Gửi request inference...")
try:
    cmd = f"""aws bedrock-runtime invoke-model --model-id {CUSTOM_MODEL_NAME} --body '{{"prompt": "Describe a futuristic city in 100 words.", "max_tokens": 512, "temperature": 0.7}}' --content-type application/json --region {AWS_REGION} output.json"""
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        print("Kết quả inference đã được lưu vào output.json")
        with open("output.json", "r") as f:
            result = json.load(f)
            print("Kết quả inference:", result)
    else:
        print(f"Lỗi khi gửi request: {result.stderr}")
        exit(1)
except Exception as e:
    print(f"Lỗi khi gửi request: {e}")
    exit(1)

print("Triển khai hoàn tất!")
