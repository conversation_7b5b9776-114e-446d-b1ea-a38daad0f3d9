import json
import base64

request = {
  "messages": [
    {
      "role": "user",
      "content": "Describe a futuristic city in 100 words."
    }
  ],
  "max_tokens": 512,
  "temperature": 0.7
}

# Convert to JSON string
json_str = json.dumps(request)

# Encode to base64
base64_str = base64.b64encode(json_str.encode()).decode()

print(base64_str)

# Save to file
with open("qwen_request_base64.txt", "w") as f:
    f.write(base64_str)
