---
- name: Deploy Embedding Model for Ollama
  hosts: all
  become: true
  vars:
    # Model configuration
    huggingface_repo: "AITeamVN/Vietnamese_Embedding_v2"  # Hugging Face repository
    model_name: "{{ huggingface_repo | regex_replace('^.*/(.*)$', '\\1') | lower | regex_replace('_', '-') }}"  # Default: derived from repo name
    model_dir: "/datastores/hf/{{ huggingface_repo | regex_replace('/', '_') }}"

    # Advanced configuration
    model_format: "f16"  # Output format for GGUF conversion (f16, f32, q4_0, q4_1, etc.)
    context_size: 2048   # Context size for the model

  tasks:
    - name: Include dependency installation tasks
      include_tasks: tasks/install_dependencies.yml

    - name: Include model download tasks
      include_tasks: tasks/download_model.yml

    - name: Include model conversion tasks
      include_tasks: tasks/convert_model.yml

    - name: Include model installation tasks
      include_tasks: tasks/install_model.yml
