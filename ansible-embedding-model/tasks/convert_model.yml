---
- name: Check if GGUF model already exists
  stat:
    path: "{{ model_dir }}/{{ model_name }}.gguf"
  register: gguf_file

- name: Build llama.cpp with CMake
  shell: |
    cd {{ model_dir }}/../llama.cpp &&
    mkdir -p build &&
    cd build &&
    cmake -DLLAMA_CURL=OFF .. &&
    cmake --build . --config Release
  args:
    creates: "{{ model_dir }}/../llama.cpp/build/bin/llama-cli"
  when: not gguf_file.stat.exists and llama_cpp_clone.changed

- name: Convert model to GGUF format
  shell: |
    source {{ model_dir }}_venv/bin/activate &&
    cd {{ model_dir }}/../llama.cpp &&
    python convert_hf_to_gguf.py --outfile {{ model_dir }}/{{ model_name }}.gguf --outtype {{ model_format }} {{ model_dir }}
  args:
    executable: /bin/bash
    creates: "{{ model_dir }}/{{ model_name }}.gguf"
  when: not gguf_file.stat.exists
