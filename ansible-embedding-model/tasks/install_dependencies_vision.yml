---
- name: Update apt cache
  apt:
    update_cache: yes
  when: ansible_os_family == "Debian"

- name: Install required packages for vision models
  apt:
    name:
      - git
      - git-lfs
      - cmake
      - build-essential
      - python3-dev
      - python3-pip
      - python3-venv
      - libcurl4-openssl-dev
      - libgl1-mesa-glx
      - libglib2.0-0
      - libsm6
      - libxext6
      - libxrender-dev
      - libgomp1
    state: present
  when: ansible_os_family == "Debian"

- name: Create Python virtual environment for vision model
  command: python3 -m venv {{ model_dir }}_venv
  args:
    creates: "{{ model_dir }}_venv"

- name: Install Python dependencies for vision models
  pip:
    name:
      - torch
      - torchvision
      - transformers>=4.37.0
      - accelerate
      - sentencepiece
      - protobuf
      - requests
      - numpy
      - pillow
      - opencv-python
      - safetensors
    virtualenv: "{{ model_dir }}_venv"
    state: present
