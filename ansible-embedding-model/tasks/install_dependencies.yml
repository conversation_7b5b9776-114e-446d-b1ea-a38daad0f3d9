---
- name: Update apt cache
  apt:
    update_cache: yes
  when: ansible_os_family == "Debian"

- name: Install required packages
  apt:
    name:
      - git
      - git-lfs
      - cmake
      - build-essential
      - python3-dev
      - python3-pip
      - python3-venv
      - libcurl4-openssl-dev
    state: present
  when: ansible_os_family == "Debian"

- name: Create Python virtual environment
  command: python3 -m venv {{ model_dir }}_venv
  args:
    creates: "{{ model_dir }}_venv"

- name: Install Python dependencies
  pip:
    name:
      - torch
      - transformers
      - sentencepiece
      - protobuf
      - requests
      - numpy
    virtualenv: "{{ model_dir }}_venv"
    state: present
