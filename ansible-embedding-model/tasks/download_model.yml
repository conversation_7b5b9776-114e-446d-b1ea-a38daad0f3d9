---
- name: Create model directory
  file:
    path: "{{ model_dir }}"
    state: directory
    mode: '0755'

- name: Check if model already exists
  stat:
    path: "{{ model_dir }}/model.safetensors"
  register: model_file

- name: Initialize Git LFS
  command: git lfs install
  when: not model_file.stat.exists

- name: Clone the model repository
  git:
    repo: "https://huggingface.co/{{ huggingface_repo }}"
    dest: "{{ model_dir }}"
    force: yes
  when: not model_file.stat.exists

- name: Clone llama.cpp repository
  git:
    repo: https://github.com/ggerganov/llama.cpp.git
    dest: "{{ model_dir }}/../llama.cpp"
    force: no
  register: llama_cpp_clone
