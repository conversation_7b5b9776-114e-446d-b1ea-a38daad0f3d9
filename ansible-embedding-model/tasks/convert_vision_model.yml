---
- name: Check if base GGUF model already exists
  stat:
    path: "{{ model_dir }}/{{ model_name }}_base.gguf"
  register: base_gguf_file

- name: Check if quantized GGUF model already exists
  stat:
    path: "{{ model_dir }}/{{ model_name }}.gguf"
  register: quantized_gguf_file

- name: Build llama.cpp with CMake
  shell: |
    cd {{ model_dir }}/../llama.cpp &&
    mkdir -p build &&
    cd build &&
    cmake -DLLAMA_CURL=OFF .. &&
    cmake --build . --config Release
  args:
    creates: "{{ model_dir }}/../llama.cpp/build/bin/llama-cli"
  when: not base_gguf_file.stat.exists and llama_cpp_clone.changed

- name: Convert model to base GGUF format (f16)
  shell: |
    source {{ model_dir }}_venv/bin/activate &&
    cd {{ model_dir }}/../llama.cpp &&
    python convert_hf_to_gguf.py --outfile {{ model_dir }}/{{ model_name }}_base.gguf --outtype {{ model_format }} {{ model_dir }}
  args:
    executable: /bin/bash
    creates: "{{ model_dir }}/{{ model_name }}_base.gguf"
  when: not base_gguf_file.stat.exists

- name: Quantize model to final format
  shell: |
    cd {{ model_dir }}/../llama.cpp &&
    ./build/bin/llama-quantize {{ model_dir }}/{{ model_name }}_base.gguf {{ model_dir }}/{{ model_name }}.gguf {{ quantize_format }}
  args:
    creates: "{{ model_dir }}/{{ model_name }}.gguf"
  when: not quantized_gguf_file.stat.exists and quantize_format != model_format

- name: Copy base model as final model (if no quantization needed)
  copy:
    src: "{{ model_dir }}/{{ model_name }}_base.gguf"
    dest: "{{ model_dir }}/{{ model_name }}.gguf"
    remote_src: yes
  when: not quantized_gguf_file.stat.exists and quantize_format == model_format

- name: Remove base model file to save space (optional)
  file:
    path: "{{ model_dir }}/{{ model_name }}_base.gguf"
    state: absent
  when: quantize_format != model_format and quantized_gguf_file.stat.exists
