---
- name: Create Modelfile
  copy:
    dest: "{{ model_dir }}/Modelfile"
    content: |
      FROM {{ model_dir }}/{{ model_name }}.gguf
      TEMPLATE "{{ '{{' }} .Prompt {{ '}}' }}"
      PARAMETER stop ""
      PARAMETER num_ctx {{ context_size }}
    mode: '0644'

- name: Check if model is already installed in Ollama
  shell: ollama list | grep {{ model_name }}
  register: ollama_model_check
  ignore_errors: yes
  changed_when: false

- name: Create model in Ollama
  shell: cd {{ model_dir }} && ollama create {{ model_name }} -f Modelfile
  when: ollama_model_check.rc != 0

- name: Test the model with a simple embedding
  shell: |
    source {{ model_dir }}_venv/bin/activate &&
    python3 -c "
    import requests
    import json

    response = requests.post('http://localhost:11434/api/embeddings',
                           json={'model': '{{ model_name }}',
                                'prompt': 'Trí tuệ nhân tạo là gì'})

    # Just print the first 5 dimensions of the embedding to verify it works
    embedding = response.json()['embedding'][:5]
    print(f'First 5 dimensions of embedding: {embedding}')
    "
  args:
    executable: /bin/bash
  register: embedding_test
  changed_when: false

- name: Show embedding test result
  debug:
    var: embedding_test.stdout
