# Ansible Playbook cho Embedding Model

Playbook này tự động hóa quá trình triển khai mô hình Embedding từ Hugging Face cho Ollama. Mặc định, playbook sẽ triển khai mô hình Vietnamese Embedding, nhưng bạn có thể dễ dàng cấu hình để triển khai bất kỳ mô hình embedding nào từ Hugging Face.

## Yêu cầu

- Ansible đã được cài đặt trên máy điều khiển
- Ollama đã được cài đặt trên máy chủ đích
- Kết nối SSH đến máy chủ đích với quyền root hoặc sudo

## Cấu trúc thư mục

```
ansible-embedding-model/
├── inventory.ini                # Danh sách máy chủ đích
├── embedding_model_playbook.yml  # Playbook chính
├── tasks/
│   ├── install_dependencies.yml # Cài đặt các gói phụ thuộc
│   ├── download_model.yml       # Tải mô hình từ Hugging Face
│   ├── convert_model.yml        # Chuyển đổi mô hình sang định dạng GGUF
│   └── install_model.yml        # Cài đặt mô hình vào Ollama
├── deploy.sh                    # Script để chạy playbook
└── README.md                    # Tài liệu hướng dẫn
```

## Cách sử dụng

1. Chỉnh sửa file `inventory.ini` để thêm địa chỉ IP của máy chủ đích:

```ini
[embedding_servers]
your_server_ip ansible_user=root
```

2. Chạy playbook bằng script `deploy.sh`:

```bash
./deploy.sh
```

Hoặc sử dụng trực tiếp Ansible:

```bash
ansible-playbook -i inventory.ini embedding_model_playbook.yml
```

## Biến số

Các biến số có thể được điều chỉnh trong file `embedding_model_playbook.yml` hoặc truyền qua command line với tham số `-e`:

### Biến số cơ bản

- `huggingface_repo`: Repository của mô hình trên Hugging Face (mặc định: "AITeamVN/Vietnamese_Embedding_v2")
- `model_name`: Tên mô hình trong Ollama (mặc định: tự động tạo từ tên repository)
- `model_dir`: Thư mục lưu trữ mô hình (mặc định: tự động tạo từ tên repository)

### Biến số nâng cao

- `model_format`: Định dạng đầu ra cho quá trình chuyển đổi GGUF (mặc định: "f16", các tùy chọn khác: "f32", "q4_0", "q4_1", v.v.)
- `context_size`: Kích thước ngữ cảnh cho mô hình (mặc định: 2048)

### Ví dụ sử dụng biến số

Sử dụng script `deploy.sh`:

```bash
# Triển khai mô hình embedding khác
./deploy.sh --repo sentence-transformers/all-MiniLM-L6-v2

# Thay đổi định dạng mô hình và kích thước ngữ cảnh
./deploy.sh --format q4_0 --context 4096

# Kết hợp nhiều tùy chọn
./deploy.sh --repo sentence-transformers/all-MiniLM-L6-v2 --format q4_0 --context 4096 --name my-embedding-model
```

Sử dụng trực tiếp Ansible:

```bash
# Triển khai mô hình embedding khác
ansible-playbook -i inventory.ini embedding_model_playbook.yml -e "huggingface_repo=sentence-transformers/all-MiniLM-L6-v2"

# Thay đổi định dạng mô hình và kích thước ngữ cảnh
ansible-playbook -i inventory.ini embedding_model_playbook.yml -e "model_format=q4_0 context_size=4096"
```

## Kiểm tra

Sau khi triển khai, playbook sẽ tự động kiểm tra mô hình bằng cách tạo embedding cho một câu tiếng Việt đơn giản.
