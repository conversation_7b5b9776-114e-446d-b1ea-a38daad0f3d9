---
- name: Deploy GOT-OCR-2.0-hf Model for Ollama
  hosts: all
  become: true
  vars:
    # Model configuration
    huggingface_repo: "stepfun-ai/GOT-OCR-2.0-hf"  # Hugging Face repository
    model_name: "got-ocr-2"  # Simplified name for easier use
    model_dir: "/datastores/hf/{{ huggingface_repo | regex_replace('/', '_') }}"

    # Advanced configuration optimized for GOT-OCR
    model_format: "f16"   # Base format for conversion (will quantize to q4_0 later)
    quantize_format: "q4_0"  # Final quantized format
    context_size: 8192   # Larger context for complex OCR tasks

  tasks:
    - name: Include dependency installation tasks for vision models
      include_tasks: tasks/install_dependencies_vision.yml

    - name: Include model download tasks
      include_tasks: tasks/download_model.yml

    - name: Include model conversion tasks
      include_tasks: tasks/convert_model.yml

    - name: Include vision model installation tasks
      include_tasks: tasks/install_vision_model.yml
