# GOT-OCR-2.0-hf Deployment Guide

## Tổng quan

Script này tự động cài đặt mô hình GOT-OCR-2.0-hf từ stepfun-ai với cấu hình tối ưu cho các tác vụ OCR.

## Cấu hình mặc định

- **Model**: stepfun-ai/GOT-OCR-2.0-hf
- **Tên trong Ollama**: got-ocr-2
- **Format**: q4_0 (quantized cho hiệu suất tốt)
- **Context size**: 8192 (phù hợp cho OCR phức tạp)
- **Temperature**: 0.0 (deterministic cho OCR chính xác)

## Cách sử dụng

### Cài đặt với cấu hình mặc định
```bash
cd ansible-embedding-model
./deploy_got_ocr.sh
```

### Cài đặt với tùy chọn
```bash
# Sử dụng format f16 thay vì q4_0
./deploy_got_ocr.sh --format f16

# Thay đổi context size
./deploy_got_ocr.sh --context 4096
```

## Khả năng của GOT-OCR-2.0

Model này hỗ trợ nhiều loại OCR:

1. **Plain text OCR**: Nhận dạng văn bản thông thường
2. **Formatted document OCR**: Tài liệu có định dạng
3. **Table OCR**: Bảng biểu
4. **Mathematical formulas**: Công thức toán học
5. **Charts and diagrams**: Biểu đồ
6. **Sheet music**: Nhạc phổ
7. **Interactive OCR**: OCR theo vùng chỉ định

## Cách sử dụng sau khi cài đặt

### 1. OCR cơ bản với curl
```bash
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "got-ocr-2",
    "prompt": "Extract text from this image",
    "images": ["base64_encoded_image_here"]
  }'
```

### 2. OCR với định dạng
```bash
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "got-ocr-2",
    "prompt": "Extract text with formatting (markdown/latex)",
    "images": ["base64_encoded_image_here"]
  }'
```

### 3. OCR cho bảng biểu
```bash
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "got-ocr-2",
    "prompt": "Extract table data and format as markdown table",
    "images": ["base64_encoded_image_here"]
  }'
```

### 4. OCR cho công thức toán học
```bash
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "got-ocr-2",
    "prompt": "Extract mathematical formulas in LaTeX format",
    "images": ["base64_encoded_image_here"]
  }'
```

## Yêu cầu hệ thống

- **RAM**: Tối thiểu 8GB, khuyến nghị 16GB+
- **Storage**: ~10GB cho model và dependencies
- **GPU**: Không bắt buộc nhưng sẽ tăng tốc độ xử lý
- **Network**: Kết nối internet ổn định để tải model

## Lưu ý quan trọng

1. **Thời gian cài đặt**: 30-60 phút tùy thuộc vào tốc độ mạng
2. **Model size**: ~561M parameters, khá lớn
3. **Quantization**: Sử dụng q4_0 để cân bằng chất lượng và hiệu suất
4. **Context size**: 8192 tokens phù hợp cho hầu hết tác vụ OCR

## Troubleshooting

### Model không phản hồi
- Kiểm tra Ollama service: `systemctl status ollama`
- Restart Ollama: `systemctl restart ollama`

### Lỗi memory
- Giảm context size: `./deploy_got_ocr.sh --context 4096`
- Sử dụng quantization thấp hơn: `./deploy_got_ocr.sh --format q8_0`

### Chất lượng OCR không tốt
- Thử format f16: `./deploy_got_ocr.sh --format f16`
- Tăng context size: `./deploy_got_ocr.sh --context 16384`

## Tài liệu tham khảo

- [GOT-OCR-2.0 Paper](https://arxiv.org/abs/2409.01704)
- [Hugging Face Model Page](https://huggingface.co/stepfun-ai/GOT-OCR-2.0-hf)
- [GitHub Repository](https://github.com/Ucas-HaoranWei/GOT-OCR2.0/)
