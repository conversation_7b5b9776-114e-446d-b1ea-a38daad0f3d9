#!/bin/bash

# Script để triển khai mô hình GOT-OCR-2.0-hf

# Hi<PERSON><PERSON> thị hướng dẫn sử dụng
show_help() {
    echo "Sử dụng: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help                   Hi<PERSON><PERSON> thị hướng dẫn này"
    echo "  -r, --repo REPO              Repository của mô hình trên Hugging Face (mặc định: stepfun-ai/GOT-OCR-2.0-hf)"
    echo "  -n, --name NAME              Tên mô hình trong Ollama (mặc định: tự động tạo từ tên repository)"
    echo "  -d, --dir DIR                Thư mục lưu trữ mô hình (mặc định: tự động tạo từ tên repository)"
    echo "  -f, --format FORMAT          Định dạng đầu ra cho quá trình chuyển đổi GGUF (mặc định: f16)"
    echo "  -c, --context SIZE           K<PERSON>ch thước ngữ cảnh cho mô hình (mặc định: 4096)"
    echo ""
    echo "Ví dụ:"
    echo "  $0 --format q4_0 --context 8192"
    exit 0
}

# Xử lý tham số dòng lệnh
EXTRA_VARS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            ;;
        -r|--repo)
            EXTRA_VARS="$EXTRA_VARS huggingface_repo=$2"
            shift 2
            ;;
        -n|--name)
            EXTRA_VARS="$EXTRA_VARS model_name=$2"
            shift 2
            ;;
        -d|--dir)
            EXTRA_VARS="$EXTRA_VARS model_dir=$2"
            shift 2
            ;;
        -f|--format)
            EXTRA_VARS="$EXTRA_VARS model_format=$2"
            shift 2
            ;;
        -c|--context)
            EXTRA_VARS="$EXTRA_VARS context_size=$2"
            shift 2
            ;;
        *)
            echo "Tham số không hợp lệ: $1"
            show_help
            ;;
    esac
done

# Kiểm tra xem Ansible đã được cài đặt chưa
if ! command -v ansible &> /dev/null; then
    echo "Ansible chưa được cài đặt. Vui lòng cài đặt Ansible trước khi chạy script này."
    exit 1
fi

# Kiểm tra xem inventory.ini có tồn tại không
if [ ! -f "inventory.ini" ]; then
    echo "File inventory.ini không tồn tại. Vui lòng tạo file này với thông tin máy chủ đích."
    exit 1
fi

echo "Bắt đầu triển khai mô hình GOT-OCR-2.0-hf..."
echo "Lưu ý: Đây là một mô hình lớn (~561M parameters), quá trình tải xuống có thể mất thời gian."

# Chạy playbook
if [ -z "$EXTRA_VARS" ]; then
    ansible-playbook -i inventory.ini got_ocr_playbook.yml --ask-become-pass
else
    ansible-playbook -i inventory.ini got_ocr_playbook.yml -e "$EXTRA_VARS" --ask-become-pass
fi

# Kiểm tra kết quả
if [ $? -eq 0 ]; then
    echo "Triển khai mô hình GOT-OCR-2.0-hf thành công!"
    echo ""
    echo "Mô hình đã được cài đặt và có thể sử dụng với Ollama."
    echo "Để sử dụng mô hình cho OCR, bạn có thể gửi request đến Ollama API với hình ảnh."
else
    echo "Triển khai mô hình GOT-OCR-2.0-hf thất bại. Vui lòng kiểm tra lỗi ở trên."
    exit 1
fi
