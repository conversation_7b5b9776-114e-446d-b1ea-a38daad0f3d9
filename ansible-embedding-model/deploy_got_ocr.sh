#!/bin/bash

# Script để triển khai mô hình GOT-OCR-2.0-hf

# Hi<PERSON><PERSON> thị hướng dẫn sử dụng
show_help() {
    echo "Sử dụng: $0 [options]"
    echo ""
    echo "Script cài đặt GOT-OCR-2.0-hf với cấu hình tối ưu:"
    echo "- Model name: got-ocr-2"
    echo "- Format: q4_0 (quantized cho hiệu suất tốt)"
    echo "- Context size: 8192 (phù hợp cho <PERSON><PERSON> phức tạp)"
    echo ""
    echo "Options:"
    echo "  -h, --help                   Hi<PERSON><PERSON> thị hướng dẫn này"
    echo "  -f, --format FORMAT          Định dạng đầu ra (mặc định: q4_0)"
    echo "  -c, --context SIZE           Kích thước ngữ cảnh (mặc định: 8192)"
    echo ""
    echo "Ví dụ:"
    echo "  $0                           # Cài đặt với cấu hình mặc định"
    echo "  $0 --format f16              # Sử dụng format f16 thay vì q4_0"
    exit 0
}

# Xử lý tham số dòng lệnh
EXTRA_VARS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            ;;
        -f|--format)
            EXTRA_VARS="$EXTRA_VARS model_format=$2"
            shift 2
            ;;
        -c|--context)
            EXTRA_VARS="$EXTRA_VARS context_size=$2"
            shift 2
            ;;
        *)
            echo "Tham số không hợp lệ: $1"
            show_help
            ;;
    esac
done

# Kiểm tra xem Ansible đã được cài đặt chưa
if ! command -v ansible &> /dev/null; then
    echo "Ansible chưa được cài đặt. Vui lòng cài đặt Ansible trước khi chạy script này."
    exit 1
fi

# Kiểm tra xem inventory.ini có tồn tại không
if [ ! -f "inventory.ini" ]; then
    echo "File inventory.ini không tồn tại. Vui lòng tạo file này với thông tin máy chủ đích."
    exit 1
fi

echo "Bắt đầu triển khai mô hình GOT-OCR-2.0-hf với cấu hình tối ưu..."
echo "Cấu hình:"
echo "- Model: stepfun-ai/GOT-OCR-2.0-hf"
echo "- Tên trong Ollama: got-ocr-2"
echo "- Format: q4_0 (quantized)"
echo "- Context size: 8192"
echo ""
echo "Lưu ý: Đây là một mô hình lớn (~561M parameters), quá trình tải xuống và chuyển đổi có thể mất 30-60 phút."

# Chạy playbook
if [ -z "$EXTRA_VARS" ]; then
    ansible-playbook -i inventory.ini got_ocr_playbook.yml --ask-become-pass
else
    ansible-playbook -i inventory.ini got_ocr_playbook.yml -e "$EXTRA_VARS" --ask-become-pass
fi

# Kiểm tra kết quả
if [ $? -eq 0 ]; then
    echo ""
    echo "=== TRIỂN KHAI THÀNH CÔNG ==="
    echo "Mô hình GOT-OCR-2.0-hf đã được cài đặt thành công!"
    echo ""
    echo "Thông tin mô hình:"
    echo "- Tên: got-ocr-2"
    echo "- Loại: Vision-Language Model cho OCR"
    echo "- Hỗ trợ: Văn bản, bảng biểu, công thức toán, nhạc phổ"
    echo ""
    echo "Cách sử dụng:"
    echo "curl -X POST http://localhost:11434/api/generate \\"
    echo "  -H 'Content-Type: application/json' \\"
    echo "  -d '{"
    echo "    \"model\": \"got-ocr-2\","
    echo "    \"prompt\": \"Extract text from this image\","
    echo "    \"images\": [\"base64_encoded_image_here\"]"
    echo "  }'"
else
    echo "Triển khai mô hình GOT-OCR-2.0-hf thất bại. Vui lòng kiểm tra lỗi ở trên."
    exit 1
fi
