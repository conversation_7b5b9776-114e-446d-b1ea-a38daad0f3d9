# Hướng dẫn triển khai Qwen2.5-VL-32B-Instruct-AWQ lên Amazon Bedrock trên macOS

Hướng dẫn này cung cấp các bước chi tiết để triển khai mô hình **Qwen2.5-VL-32B-Instruct-AWQ** (4-bit, ~16-20GB) từ Hugging Face lên **Amazon Bedrock** trên **macOS**, sử dụng vùng **us-west-2** (rẻ nhất). Quy trình sử dụng một script Python để tải mô hình, đóng gói, đ<PERSON><PERSON> lên S3, nhậ<PERSON> v<PERSON><PERSON>rock, và tạo endpoint. Script tối ưu cho MacBook không có GPU, bỏ qua kiểm tra chất lượng để tiết kiệm thời gian và tài nguyên.

## 1. Tổng quan

- **<PERSON><PERSON> hình**: [Qwen2.5-VL-32B-Instruct-AWQ](https://huggingface.co/Qwen/Qwen2.5-VL-32B-Instruct-AWQ), một mô hình multimodal (văn bản + hình ảnh) đã quantize 4-bit bằng AWQ, kích thước ~16-20GB, chất lượng giảm ~1-3% so với bản gốc.
- **Amazon Bedrock**: Dịch vụ serverless của AWS để triển khai mô hình AI, yêu cầu mô hình ở định dạng Hugging Face checkpoint và lưu trữ trên S3.
- **Vùng**: **us-west-2** (Oregon), rẻ nhất theo [AWS Bedrock Pricing](https://aws.amazon.com/bedrock/pricing/).
- **Chi phí ước tính**:
  - **On-Demand (24/7)**: ~$3082.71/tháng.
  - **Batch Processing**: ~$20.66/tháng.
- **Yêu cầu phần cứng**: MacBook với ~32GB RAM, ~50GB đĩa. Nếu không đủ, dùng EC2 (`g4dn.xlarge`, ~$0.526/giờ).

## 2. Yêu cầu

### 2.1. Phần mềm
- **macOS**: Monterey (12) trở lên.
- **Python**: 3.8+.
- **AWS CLI**: Phiên bản mới nhất.
- **Thư viện Python**: `boto3`, `huggingface_hub`.

### 2.2. Tài khoản và quyền
- **AWS Account**: Có quyền quản trị IAM và Bedrock.
- **Hugging Face token**: Lấy từ [Hugging Face Settings](https://huggingface.co/settings/tokens).
- **IAM Role**: Quyền `AmazonBedrockFullAccess`, `AmazonS3FullAccess`.

### 2.3. Phần cứng
- **RAM**: Tối thiểu 32GB (MacBook Pro M1 Max/M2 Max khuyến nghị).
- **Dung lượng đĩa**: ~50GB trống.
- **Lưu ý**: Nếu MacBook không đủ tài nguyên (như MacBook Air 16GB RAM), sử dụng **AWS EC2** (`g4dn.xlarge`).

## 3. Cài đặt môi trường

### 3.1. Cài đặt AWS CLI
1. Tải AWS CLI cho macOS:
   ```bash
   curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
   ```
2. Cài đặt:
   ```bash
   sudo installer -pkg AWSCLIV2.pkg -target /
   ```
3. Kiểm tra phiên bản:
   ```bash
   aws --version
   ```
   Đảm bảo thấy output như `aws-cli/2.x.x`.

4. Cấu hình AWS CLI:
   ```bash
   aws configure
   ```
   Nhập thông tin:
   - **AWS Access Key ID**: Lấy từ [IAM Console](https://console.aws.amazon.com/iam) (Users > Security credentials > Create access key).
   - **AWS Secret Access Key**: Từ IAM Console.
   - **Default region name**: `us-west-2`.
   - **Default output format**: `json`.

### 3.2. Cài đặt Python và thư viện
1. Kiểm tra Python (macOS thường có sẵn Python 3):
   ```bash
   python3 --version
   ```
   Đảm bảo phiên bản >= 3.8. Nếu không, cài Python từ [python.org](https://www.python.org/downloads/).

2. Cài đặt thư viện:
   ```bash
   python3 -m pip install boto3 huggingface_hub
   ```

### 3.3. Tạo IAM Role
1. Vào [IAM Console](https://console.aws.amazon.com/iam).
2. Tạo role:
   - Chọn **AWS service** > **Bedrock**.
   - Gắn chính sách: `AmazonBedrockFullAccess`, `AmazonS3FullAccess`.
   - Đặt tên role, ví dụ: `BedrockExecutionRole`.
3. Lưu **ARN** của role (ví dụ: `arn:aws:iam::<account-id>:role/BedrockExecutionRole`).

### 3.4. Lấy Hugging Face token
1. Đăng nhập [Hugging Face](https://huggingface.co/).
2. Vào [Settings > Access Tokens](https://huggingface.co/settings/tokens).
3. Tạo token mới (quyền **read**).
4. Lưu token (dạng `hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxx`).

## 4. Chuẩn bị mô hình
- **Mô hình**: Qwen2.5-VL-32B-Instruct-AWQ (~16-20GB) từ Hugging Face.
- **Local (tùy chọn)**: Nếu đã tải mô hình vào thư mục `./qwen-awq-model` (định dạng Hugging Face checkpoint, không phải GGUF), script sẽ dùng trực tiếp.
- **Tải từ Hugging Face**: Nếu không có local, script sẽ tải từ [Qwen/Qwen2.5-VL-32B-Instruct-AWQ](https://huggingface.co/Qwen/Qwen2.5-VL-32B-Instruct-AWQ).

## 5. Script triển khai

Lưu script dưới đây vào file `deploy_qwen_awq_to_bedrock.py`:

```python
import boto3
import json
import os
import tarfile
import time
from huggingface_hub import login, snapshot_download

# Cấu hình
HF_TOKEN = "hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxx"  # Thay bằng Hugging Face token
AWS_REGION = "us-west-2"
S3_BUCKET = "your-bucket-name"  # Thay bằng tên bucket độc nhất
MODEL_NAME = "Qwen/Qwen2.5-VL-32B-Instruct-AWQ"
CUSTOM_MODEL_NAME = "qwen-32b-vl-awq"
ROLE_ARN = "arn:aws:iam::<account-id>:role/BedrockExecutionRole"  # Thay <account-id>
LOCAL_MODEL_DIR = "./qwen-awq-model"
S3_MODEL_PATH = f"s3://{S3_BUCKET}/qwen-awq-model.tar.gz"
ENDPOINT_NAME = "qwen-32b-awq-endpoint"
JOB_NAME = "import-qwen-awq"

# Bước 1: Kiểm tra mô hình local hoặc tải từ Hugging Face
print("Kiểm tra mô hình local hoặc tải từ Hugging Face...")
try:
    if os.path.exists(LOCAL_MODEL_DIR) and os.listdir(LOCAL_MODEL_DIR):
        print(f"Mô hình đã có tại {LOCAL_MODEL_DIR}. Bỏ qua tải.")
    else:
        login(token=HF_TOKEN)
        snapshot_download(
            repo_id=MODEL_NAME,
            local_dir=LOCAL_MODEL_DIR,
            local_dir_use_symlinks=False,
            token=HF_TOKEN
        )
        print(f"Đã tải mô hình {MODEL_NAME} vào {LOCAL_MODEL_DIR}.")
except Exception as e:
    print(f"Lỗi khi tải mô hình: {e}")
    exit(1)

# Bước 2: Đóng gói mô hình
print("Đóng gói mô hình...")
try:
    with tarfile.open("qwen-awq-model.tar.gz", "w:gz") as tar:
        tar.add(LOCAL_MODEL_DIR, arcname=os.path.basename(LOCAL_MODEL_DIR))
    print("Đã tạo file qwen-awq-model.tar.gz.")
except Exception as e:
    print(f"Lỗi khi đóng gói: {e}")
    exit(1)

# Bước 3: Tải lên S3
print("Tải mô hình lên S3...")
s3_client = boto3.client("s3", region_name=AWS_REGION)
try:
    try:
        s3_client.create_bucket(
            Bucket=S3_BUCKET,
            CreateBucketConfiguration={"LocationConstraint": AWS_REGION}
        )
    except (s3_client.exceptions.BucketAlreadyOwnedByYou, s3_client.exceptions.BucketAlreadyExists):
        pass
    s3_client.upload_file("qwen-awq-model.tar.gz", S3_BUCKET, "qwen-awq-model.tar.gz")
    print(f"Đã tải mô hình lên {S3_MODEL_PATH}.")
except Exception as e:
    print(f"Lỗi khi tải lên S3: {e}")
    exit(1)

# Bước 4: Nhập mô hình vào Bedrock
print("Nhập mô hình vào Bedrock...")
bedrock_client = boto3.client("bedrock", region_name=AWS_REGION)
try:
    response = bedrock_client.create_model_import_job(
        JobName=JOB_NAME,
        ModelName=CUSTOM_MODEL_NAME,
        ModelSourceS3Uri=S3_MODEL_PATH,
        ModelDataFormat="HUGGING_FACE",
        RoleArn=ROLE_ARN
    )
    job_arn = response["JobArn"]
    print(f"Đã tạo import job: {job_arn}")
    while True:
        status = bedrock_client.describe_model_import_job(JobName=JOB_NAME)
        job_status = status["JobStatus"]
        print(f"Import job status: {job_status}")
        if job_status in ["COMPLETED", "FAILED", "STOPPED"]:
            if job_status != "COMPLETED":
                print("Import job thất bại.")
                exit(1)
            break
        time.sleep(30)
except Exception as e:
    print(f"Lỗi khi nhập mô hình: {e}")
    exit(1)

# Bước 5: Tạo endpoint
print("Tạo endpoint...")
try:
    bedrock_client.create_model_invocation_endpoint(
        ModelId=CUSTOM_MODEL_NAME,
        EndpointName=ENDPOINT_NAME,
        InferenceType="ON_DEMAND"
    )
    print(f"Đã tạo endpoint: {ENDPOINT_NAME}")
except Exception as e:
    print(f"Lỗi khi tạo endpoint: {e}")
    exit(1)

# Bước 6: Gửi request inference trên Bedrock
print("Gửi request inference...")
runtime_client = boto3.client("bedrock-runtime", region_name=AWS_REGION)
try:
    response = runtime_client.invoke_model(
        modelId=CUSTOM_MODEL_NAME,
        body=json.dumps({
            "prompt": "Describe a futuristic city in 100 words.",
            "max_tokens": 512,
            "temperature": 0.7
        }),
        contentType="application/json"
    )
    result = json.loads(response["body"].read().decode())
    print("Kết quả inference:", result)
except Exception as e:
    print(f"Lỗi khi gửi request: {e}")
    exit(1)

print("Triển khai hoàn tất!")
```

### 5.1. Chỉnh sửa script
1. Mở file `deploy_qwen_awq_to_bedrock.py`.
2. Cập nhật:
   - `HF_TOKEN`: Thay bằng Hugging Face token của bạn.
   - `S3_BUCKET`: Thay bằng tên bucket độc nhất (ví dụ: `my-qwen-bucket-2025`).
   - `ROLE_ARN`: Thay bằng ARN của IAM Role (ví dụ: `arn:aws:iam::123456789012:role/BedrockExecutionRole`).

### 5.2. Chạy script
1. Mở Terminal, di chuyển đến thư mục chứa script:
   ```bash
   cd /path/to/script
   ```
2. Chạy:
   ```bash
   python3 deploy_qwen_awq_to_bedrock.py
   ```

### 5.3. Quy trình script
- **Bước 1**: Kiểm tra thư mục `./qwen-awq-model`. Nếu có Hugging Face checkpoint, bỏ qua tải. Nếu không, tải mô hình AWQ từ Hugging Face (~20-40 phút, tùy mạng).
- **Bước 2**: Đóng gói mô hình thành `qwen-awq-model.tar.gz` (~5-10 phút).
- **Bước 3**: Tạo bucket S3 (nếu chưa có) và tải file `.tar.gz` lên (~10-20 phút).
- **Bước 4**: Nhập mô hình vào Bedrock qua **Custom Model Import** (~10-30 phút, tùy AWS).
- **Bước 5**: Tạo endpoint On-Demand (~5-10 phút).
- **Bước 6**: Gửi request inference thử nghiệm trên Bedrock (~1 phút).

**Thời gian tổng**: ~1-2 giờ, tùy tốc độ mạng và phần cứng.

## 6. Chi phí ước tính (us-west-2)

- **On-Demand (endpoint chạy 24/7)**:
  - **Custom Model Units (CMUs)**: ~$4.24/giờ (1 CMU) → **$3052.80/tháng** (720 giờ).
  - **Token**: ~$18.50/tháng (1M input + 1M output).
  - **Lưu trữ (S3 20GB + Bedrock)**: ~$0.46 + $1.95 = **$2.41/tháng**.
  - **Truyền dữ liệu (100GB)**: ~$9/tháng.
  - **Tổng**: ~**$3082.71/tháng**.

- **Batch Processing** (chạy khi cần):
  - **Token**: ~$9.25/tháng (1M input + 1M output).
  - **Lưu trữ**: ~$2.41/tháng.
  - **Truyền dữ liệu**: ~$9/tháng.
  - **Tổng**: ~**$20.66/tháng**.

- **EC2 (nếu dùng)**: `g4dn.xlarge` (~$0.526/giờ) → ~$378.72/tháng (nếu chạy 24/7, chỉ cần vài giờ để triển khai).

Kiểm tra chi tiết tại [AWS Bedrock Pricing](https://aws.amazon.com/bedrock/pricing/) hoặc [AWS Pricing Calculator](https://calculator.aws/).

## 7. Lưu ý

### 7.1. Yêu cầu phần cứng
- **MacBook**: Cần ~32GB RAM, ~50GB đĩa. MacBook Air (16GB RAM) có thể không đủ, gây crash khi tải/đóng gói mô hình.
- **Giải pháp nếu thiếu tài nguyên**:
  1. Thuê EC2 (`g4dn.xlarge`, ~$0.526/giờ) trên AWS Console.
  2. Cài môi trường tương tự (AWS CLI, Python, thư viện).
  3. Chạy script trên EC2, sau đó xóa instance để tiết kiệm chi phí.

### 7.2. Custom Model Import
- Tính năng **Custom Model Import** đang ở chế độ preview (tính đến 05/2025). Kiểm tra tính khả dụng:
  1. Vào [Bedrock Console](https://console.aws.amazon.com/bedrock).
  2. Chọn vùng **us-west-2**.
  3. Xem mục **Custom Models** hoặc liên hệ AWS Support.
- Nếu không khả dụng, cân nhắc **SageMaker** (~$894.24/tháng, `ml.g5.xlarge`).

### 7.3. Xử lý lỗi
- **Lỗi tải mô hình**:
  - Kiểm tra `HF_TOKEN` và kết nối mạng.
  - Đảm bảo đủ dung lượng đĩa (~50GB).
- **Lỗi S3**:
  - Kiểm tra tên bucket độc nhất.
  - Đảm bảo IAM Role có quyền `AmazonS3FullAccess`.
- **Lỗi import job thất bại**:
  - Kiểm tra định dạng mô hình (Hugging Face checkpoint, `.tar.gz`).
  - Đảm bảo `ROLE_ARN` đúng và có quyền `AmazonBedrockFullAccess`.
  - Xem log trong Bedrock Console hoặc CloudWatch.
- **Lỗi endpoint/inference**:
  - Chờ 5-10 phút sau khi tạo endpoint.
  - Kiểm tra `modelId` và định dạng request.

### 7.4. Chất lượng mô hình
- **AWQ 4-bit**: Mất mát ~1-3% so với bản gốc (FP16), tốt hơn Q4_K_M (~1-5%). Phù hợp cho văn bản (chat, trả lời câu hỏi) và hình ảnh (phân tích biểu đồ, nhận diện đối tượng).
- **Không kiểm tra chất lượng**: Script bỏ bước inference local do hạn chế GPU trên macOS. Bedrock xử lý inference trên GPU (NVIDIA A10G), đảm bảo hiệu suất.

## 8. Giải pháp thay thế

Nếu Bedrock không phù hợp (do preview hoặc chi phí cao), cân nhắc:
- **Amazon SageMaker**:
  - Hỗ trợ mô hình AWQ trực tiếp với Hugging Face Deep Learning Containers.
  - Chi phí: ~$894.24/tháng (`ml.g5.xlarge`, 24/7, us-west-2).
  - Nhược điểm: Cần quản lý instance, phức tạp hơn Bedrock.
- **Chạy local với Ollama**:
  - Dùng mô hình Q4_K_M trên máy có GPU (như NVIDIA RTX 3090).
  - Nhược điểm: Yêu cầu phần cứng mạnh, không phù hợp cho sản xuất.

## 9. Kết luận

Hướng dẫn này giúp bạn triển khai **Qwen2.5-VL-32B-Instruct-AWQ** lên **Amazon Bedrock** trên **macOS** một cách nhanh chóng, bỏ qua kiểm tra chất lượng để tránh hạn chế GPU. Script tự động hóa toàn bộ quy trình, tận dụng mô hình AWQ đã quantize (~16-20GB), tiết kiệm thời gian (~1-2 giờ) và tương thích trực tiếp với Bedrock. Chi phí ~**$3082.71/tháng** (On-Demand) hoặc ~**$20.66/tháng** (Batch) ở **us-west-2**.

Nếu gặp lỗi hoặc cần script cho SageMaker, liên hệ hỗ trợ qua [AWS Support](https://console.aws.amazon.com/support) hoặc yêu cầu thêm thông tin chi tiết.