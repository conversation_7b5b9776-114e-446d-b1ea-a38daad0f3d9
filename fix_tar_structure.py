import os
import tarfile
import boto3
import shutil
import glob

# Cấu hình
AWS_REGION = "us-west-2"
S3_BUCKET = "dbiz-llm"
LOCAL_MODEL_DIR = "./qwen-awq-model"
FIXED_TAR_FILE = "qwen-awq-model-fixed.tar.gz"

print("Kiểm tra thư mục mô hình...")
if not os.path.exists(LOCAL_MODEL_DIR):
    print(f"Thư mục {LOCAL_MODEL_DIR} không tồn tại.")
    print("Vui lòng tải mô hình từ Hugging Face trước.")
    exit(1)

# Tìm file config.json trong thư mục mô hình
config_files = glob.glob(f"{LOCAL_MODEL_DIR}/**/config.json", recursive=True)
if not config_files:
    print("Không tìm thấy file config.json trong thư mục mô hình.")
    print("Vui lòng đảm bảo mô hình có cấu trúc Hugging Face đúng.")
    exit(1)

print(f"Tìm thấy {len(config_files)} file config.json:")
for config_file in config_files:
    print(f"  {config_file}")

# Xác định thư mục gốc của mô hình
model_root_dir = os.path.dirname(config_files[0])
print(f"Thư mục gốc của mô hình: {model_root_dir}")

# Tạo file tar.gz mới với cấu trúc đúng
print(f"Tạo file {FIXED_TAR_FILE} với cấu trúc đúng...")
try:
    with tarfile.open(FIXED_TAR_FILE, "w:gz") as tar:
        # Thêm các file từ thư mục gốc của mô hình vào gốc của file tar.gz
        for item in os.listdir(model_root_dir):
            item_path = os.path.join(model_root_dir, item)
            arcname = item  # Đặt tên trong archive là tên file/thư mục
            tar.add(item_path, arcname=arcname)
    print(f"Đã tạo file {FIXED_TAR_FILE}.")
except Exception as e:
    print(f"Lỗi khi tạo file tar.gz: {e}")
    exit(1)

# Tải file tar.gz mới lên S3
print(f"Tải file {FIXED_TAR_FILE} lên S3...")
s3_client = boto3.client("s3", region_name=AWS_REGION)
try:
    s3_client.upload_file(FIXED_TAR_FILE, S3_BUCKET, FIXED_TAR_FILE)
    print(f"Đã tải file {FIXED_TAR_FILE} lên s3://{S3_BUCKET}/{FIXED_TAR_FILE}.")
except Exception as e:
    print(f"Lỗi khi tải lên S3: {e}")
    exit(1)

print("\nBước tiếp theo:")
print(f"Sử dụng file s3://{S3_BUCKET}/{FIXED_TAR_FILE} để nhập vào Bedrock.")
print("Lệnh để nhập mô hình vào Bedrock:")
print(f"aws bedrock create-model-import-job --job-name import-qwen-awq --imported-model-name qwen-32b-vl-awq --model-data-source '{{\"s3DataSource\": {{\"s3Uri\": \"s3://{S3_BUCKET}/{FIXED_TAR_FILE}\"}}}}' --role-arn arn:aws:iam::737232469904:role/BedrockImportRole --region {AWS_REGION}")
