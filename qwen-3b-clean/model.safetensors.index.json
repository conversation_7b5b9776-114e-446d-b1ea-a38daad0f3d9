{"metadata": {"total_size": 7509245952}, "weight_map": {"model.embed_tokens.weight": "model-00001-of-00002.safetensors", "model.layers.0.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.0.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.0.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.0.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.0.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.1.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.10.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.11.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.12.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.13.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.13.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.13.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.13.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.13.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.14.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.14.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.14.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.14.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.14.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.14.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.14.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.14.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.14.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.14.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.14.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.14.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.15.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.15.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.15.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.15.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.15.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.15.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.15.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.15.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.15.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.15.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.15.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.15.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.16.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.16.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.16.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.16.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.16.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.16.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.16.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.16.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.16.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.16.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.16.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.16.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.17.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.17.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.17.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.17.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.17.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.17.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.17.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.17.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.17.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.17.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.17.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.17.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.18.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.18.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.18.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.18.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.18.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.18.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.18.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.18.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.18.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.18.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.18.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.18.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.19.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.19.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.19.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.19.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.19.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.19.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.19.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.19.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.19.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.19.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.19.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.19.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.2.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.2.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.20.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.20.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.20.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.20.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.20.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.20.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.20.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.20.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.20.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.20.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.20.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.20.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.21.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.21.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.21.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.21.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.21.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.21.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.21.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.21.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.21.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.21.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.21.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.21.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.22.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.22.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.22.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.22.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.22.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.22.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.22.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.22.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.22.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.22.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.22.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.22.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.23.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.23.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.23.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.23.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.23.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.23.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.23.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.23.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.23.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.23.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.23.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.23.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.24.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.24.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.24.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.24.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.24.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.24.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.24.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.24.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.24.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.24.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.24.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.24.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.25.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.25.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.25.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.25.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.25.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.25.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.25.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.25.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.25.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.25.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.25.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.25.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.26.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.26.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.26.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.26.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.26.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.26.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.26.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.26.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.26.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.26.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.26.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.26.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.27.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.27.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.27.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.27.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.27.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.27.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.27.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.27.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.27.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.27.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.27.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.27.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.28.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.28.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.28.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.28.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.28.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.28.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.28.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.28.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.28.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.28.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.28.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.28.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.29.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.29.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.29.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.29.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.29.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.29.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.29.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.29.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.29.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.29.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.29.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.29.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.3.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.3.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.30.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.30.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.30.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.30.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.30.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.30.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.30.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.30.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.30.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.30.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.30.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.30.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.31.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.31.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.31.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.31.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.31.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.31.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.31.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.31.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.31.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.31.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.31.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.31.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.32.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.32.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.32.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.32.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.32.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.32.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.32.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.32.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.32.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.32.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.32.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.32.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.33.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.33.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.33.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.33.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.33.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.33.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.33.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.33.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.33.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.33.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.33.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.33.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.34.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.34.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.34.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.34.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.34.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.34.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.34.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.34.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.34.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.34.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.34.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.34.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.35.input_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.35.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "model.layers.35.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "model.layers.35.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "model.layers.35.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "model.layers.35.self_attn.k_proj.bias": "model-00002-of-00002.safetensors", "model.layers.35.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "model.layers.35.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "model.layers.35.self_attn.q_proj.bias": "model-00002-of-00002.safetensors", "model.layers.35.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "model.layers.35.self_attn.v_proj.bias": "model-00002-of-00002.safetensors", "model.layers.35.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "model.layers.4.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.4.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.5.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.6.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.7.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.8.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.9.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.norm.weight": "model-00002-of-00002.safetensors", "visual.blocks.0.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.0.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.0.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.0.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.0.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.0.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.0.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.0.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.0.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.0.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.0.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.0.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.1.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.1.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.1.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.1.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.1.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.1.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.1.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.1.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.1.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.1.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.1.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.1.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.10.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.10.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.10.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.10.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.10.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.10.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.10.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.10.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.10.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.10.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.10.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.10.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.11.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.11.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.11.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.11.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.11.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.11.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.11.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.11.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.11.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.11.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.11.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.11.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.12.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.12.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.12.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.12.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.12.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.12.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.12.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.12.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.12.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.12.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.12.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.12.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.13.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.13.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.13.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.13.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.13.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.13.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.13.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.13.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.13.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.13.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.13.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.13.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.14.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.14.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.14.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.14.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.14.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.14.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.14.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.14.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.14.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.14.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.14.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.14.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.15.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.15.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.15.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.15.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.15.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.15.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.15.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.15.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.15.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.15.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.15.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.15.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.16.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.16.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.16.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.16.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.16.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.16.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.16.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.16.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.16.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.16.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.16.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.16.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.17.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.17.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.17.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.17.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.17.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.17.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.17.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.17.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.17.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.17.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.17.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.17.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.18.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.18.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.18.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.18.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.18.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.18.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.18.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.18.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.18.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.18.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.18.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.18.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.19.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.19.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.19.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.19.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.19.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.19.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.19.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.19.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.19.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.19.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.19.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.19.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.2.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.2.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.2.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.2.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.2.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.2.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.2.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.2.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.2.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.2.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.2.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.2.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.20.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.20.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.20.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.20.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.20.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.20.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.20.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.20.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.20.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.20.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.20.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.20.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.21.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.21.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.21.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.21.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.21.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.21.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.21.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.21.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.21.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.21.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.21.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.21.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.22.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.22.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.22.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.22.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.22.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.22.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.22.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.22.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.22.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.22.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.22.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.22.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.23.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.23.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.23.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.23.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.23.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.23.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.23.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.23.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.23.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.23.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.23.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.23.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.24.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.24.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.24.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.24.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.24.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.24.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.24.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.24.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.24.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.24.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.24.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.24.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.25.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.25.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.25.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.25.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.25.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.25.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.25.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.25.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.25.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.25.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.25.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.25.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.26.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.26.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.26.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.26.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.26.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.26.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.26.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.26.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.26.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.26.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.26.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.26.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.27.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.27.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.27.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.27.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.27.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.27.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.27.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.27.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.27.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.27.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.27.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.27.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.28.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.28.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.28.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.28.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.28.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.28.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.28.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.28.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.28.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.28.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.28.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.28.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.29.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.29.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.29.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.29.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.29.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.29.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.29.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.29.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.29.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.29.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.29.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.29.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.3.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.3.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.3.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.3.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.3.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.3.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.3.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.3.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.3.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.3.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.3.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.3.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.30.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.30.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.30.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.30.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.30.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.30.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.30.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.30.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.30.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.30.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.30.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.30.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.31.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.31.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.31.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.31.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.31.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.31.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.31.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.31.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.31.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.31.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.31.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.31.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.4.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.4.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.4.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.4.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.4.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.4.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.4.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.4.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.4.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.4.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.4.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.4.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.5.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.5.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.5.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.5.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.5.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.5.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.5.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.5.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.5.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.5.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.5.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.5.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.6.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.6.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.6.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.6.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.6.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.6.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.6.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.6.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.6.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.6.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.6.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.6.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.7.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.7.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.7.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.7.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.7.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.7.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.7.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.7.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.7.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.7.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.7.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.7.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.8.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.8.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.8.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.8.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.8.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.8.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.8.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.8.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.8.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.8.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.8.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.8.norm2.weight": "model-00001-of-00002.safetensors", "visual.blocks.9.attn.proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.9.attn.proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.9.attn.qkv.bias": "model-00001-of-00002.safetensors", "visual.blocks.9.attn.qkv.weight": "model-00001-of-00002.safetensors", "visual.blocks.9.mlp.down_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.9.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.9.mlp.gate_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.9.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.9.mlp.up_proj.bias": "model-00001-of-00002.safetensors", "visual.blocks.9.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "visual.blocks.9.norm1.weight": "model-00001-of-00002.safetensors", "visual.blocks.9.norm2.weight": "model-00001-of-00002.safetensors", "visual.merger.ln_q.weight": "model-00001-of-00002.safetensors", "visual.merger.mlp.0.bias": "model-00001-of-00002.safetensors", "visual.merger.mlp.0.weight": "model-00001-of-00002.safetensors", "visual.merger.mlp.2.bias": "model-00001-of-00002.safetensors", "visual.merger.mlp.2.weight": "model-00001-of-00002.safetensors", "visual.patch_embed.proj.weight": "model-00001-of-00002.safetensors"}}