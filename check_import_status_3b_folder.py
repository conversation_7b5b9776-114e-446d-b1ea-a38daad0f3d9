import subprocess
import json
import time

JOB_NAME = "import-qwen-3b-folder"
REGION = "us-west-2"

def check_status():
    cmd = f"aws bedrock get-model-import-job --job-identifier {JOB_NAME} --region {REGION}"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        data = json.loads(result.stdout)
        status = data.get("status")
        last_modified = data.get("lastModifiedTime")
        print(f"Trạng thái: {status}")
        print(f"Cập nhật lần cuối: {last_modified}")
        
        if status == "COMPLETED":
            print("Nhập mô hình đã hoàn tất thành công!")
            print("Bạn có thể tạo endpoint với lệnh:")
            print(f"aws bedrock create-model-invocation-endpoint --model-id qwen-3b-awq-folder --endpoint-name qwen-3b-awq-endpoint --inference-type ON_DEMAND --region {REGION}")
            return True
        elif status in ["FAILED", "STOPPED"]:
            print("Nhập mô hình thất bại hoặc đã bị dừng.")
            if "failureMessage" in data:
                print(f"Lỗi: {data['failureMessage']}")
            return True
    else:
        print(f"Lỗi khi kiểm tra trạng thái: {result.stderr}")
    
    return False

print("Đang kiểm tra trạng thái nhập mô hình...")
print("Nhấn Ctrl+C để dừng.")

try:
    while True:
        if check_status():
            break
        print("\nĐợi 60 giây trước khi kiểm tra lại...")
        time.sleep(60)
except KeyboardInterrupt:
    print("\nĐã dừng kiểm tra.")
